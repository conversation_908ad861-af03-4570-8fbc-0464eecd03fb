import 'server-only'
import fs from 'fs/promises'
import path from 'path'

const categoriesPath = path.join(process.cwd(), 'content/categories.json')

interface CategoryData {
  name: string
  slug: string
  description: string
  image: string
  color?: string
}

interface CategoriesData {
  categories: CategoryData[]
}

// Default category data to use if the file doesn't exist
const DEFAULT_CATEGORIES: CategoriesData = {
  categories: [
    {
      name: 'Uncategorized',
      slug: 'uncategorized',
      description: 'Posts that have not been categorized yet.',
      image: '/images/categories/default.jpg',
      color: '#6B7280'
    }
  ]
}

// Cache for categories data
let categoriesCache: CategoriesData | null = null

/**
 * Get all categories from the JSON file (server-side only)
 */
async function getCategoriesData(): Promise<CategoriesData> {
  if (categoriesCache) {
    return categoriesCache
  }

  try {
    const fileContents = await fs.readFile(categoriesPath, 'utf8')
    categoriesCache = JSON.parse(fileContents) as CategoriesData
  } catch (error) {
    console.error('Error reading categories file, using default categories:', error)
    categoriesCache = DEFAULT_CATEGORIES
  }

  return categoriesCache || DEFAULT_CATEGORIES
}

/**
 * Get all categories with their post counts (server-side only)
 */
export async function getCategoriesWithPostCounts() {
  const { getAllBlogPosts } = await import('@/lib/mdx')
  const { categories } = await getCategoriesData()
  const posts = await getAllBlogPosts()
  
  return categories.map(category => {
    const postCount = posts.filter(post => 
      post.categories?.some(cat => 
        cat.toLowerCase() === category.name.toLowerCase() ||
        cat.toLowerCase().replace(/\s+/g, '-') === category.slug
      )
    ).length
    
    return {
      ...category,
      count: postCount
    }
  }).filter(category => category.count > 0) // Only include categories with posts
}

/**
 * Get a category by its slug (server-side only)
 */
export async function getCategoryBySlug(slug: string): Promise<CategoryData | null> {
  console.log('[getCategoryBySlug] Looking for slug:', slug)
  const { categories } = await getCategoriesData()
  console.log('[getCategoryBySlug] Available categories:', categories.map(c => c.slug))
  const found = categories.find(cat => cat.slug === slug) || null
  console.log('[getCategoryBySlug] Found category:', found)
  return found
}

/**
 * Get all categories (server-side only)
 */
export async function getAllCategories(): Promise<CategoryData[]> {
  const { categories } = await getCategoriesData()
  return categories
}

/**
 * Get multiple categories by their slugs (server-side only)
 */
export async function getCategoriesBySlugs(slugs: string[]): Promise<CategoryData[]> {
  const { categories } = await getCategoriesData()
  return categories.filter(cat => slugs.includes(cat.slug))
}

/**
 * Get the default category
 */
export async function getDefaultCategory(): Promise<CategoryData> {
  const data = await getCategoriesData()
  return data.categories[0] || DEFAULT_CATEGORIES.categories[0]
}
