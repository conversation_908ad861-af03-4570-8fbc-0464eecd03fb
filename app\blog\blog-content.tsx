import { getPublishedBlogPosts } from "@/lib/mdx"
import { InfiniteScroll } from "@/components/blog/infinite-scroll"
import { searchPosts } from "@/lib/search"
import type { BlogPost } from "@/types/blog"
import { Container } from "@/components/ui/container"
import Link from "next/link"
import { BlogCard } from "@/components/blog/blog-card"
import { getCategoriesWithPostCounts } from "@/lib/categories"
import Image from "next/image"
import { cn } from "@/lib/utils"

export default async function BlogContent({ searchParams }: {
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  const query = typeof searchParams.q === "string" ? searchParams.q : ""

  let blogPosts: BlogPost[] = [];
  let categories: any[] = [];
  try {
    blogPosts = await getPublishedBlogPosts() || [];
    categories = await getCategoriesWithPostCounts() || [];
  } catch (error) {
    console.error('Error loading blog posts:', error);
  }
  
  const filteredPosts = query ? searchPosts(blogPosts, query) : blogPosts

  // For search results, show infinite scroll immediately
  if (query) {
    const initialPosts = filteredPosts.slice(0, 12)
    return (
      <Container as="main" className="py-8">
        <div className="space-y-8">
          <div className="space-y-4">
            <h1 className="text-3xl font-bold tracking-tight">Search Results</h1>
            <p className="text-sm text-muted-foreground">
              Found {filteredPosts.length} result{filteredPosts.length === 1 ? "" : "s"} for &quot;{query}&quot;
            </p>
          </div>
          <InfiniteScroll
            initialPosts={initialPosts}
            searchQuery={query}
          />
        </div>
      </Container>
    )
  }

  // Handle case when there are no blog posts
  if (blogPosts.length === 0) {
    return (
      <Container as="main" className="py-8">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold mb-4">No blog posts found</h1>
          <p className="text-muted-foreground">
            We couldn&apos;t find any blog posts at the moment. Please check back later.
          </p>
        </div>
      </Container>
    )
  }

  // Rest of your blog page content...
  const featuredPost = blogPosts[0];
  const sidePosts = blogPosts.slice(1, 5);

  return (
    <Container as="main" className="py-6">
      {/* Hero Section with Featured Post and Latest Posts */}
      <div className="mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Featured Post - More Compact */}
          <div className="lg:col-span-2">
            <h2 className="text-xl font-bold mb-4">Featured Post</h2>
            <div className="bg-card rounded-lg overflow-hidden border border-border">
              <BlogCard post={featuredPost} featured />
            </div>
          </div>

          {/* Latest Posts Sidebar */}
          <div className="lg:col-span-1">
            <h2 className="text-xl font-bold mb-4">Latest Posts</h2>
            <div className="space-y-4">
              {sidePosts.map((post) => (
                <BlogCard key={post.slug} post={post} compact />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* More Posts Grid */}
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">More Articles</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {blogPosts.slice(5, 11).map((post) => (
            <BlogCard key={post.slug} post={post} />
          ))}
        </div>
      </div>

      {/* Categories */}
      {categories.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-bold mb-4">Browse by Category</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {categories.slice(0, 8).map((category) => (
              <Link
                key={category.slug}
                href={`/blog/category/${category.slug}`}
                className={cn(
                  "group flex flex-col h-full rounded-lg overflow-hidden border border-border",
                  "hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                )}
                style={category.color ? { '--category-color': category.color } as React.CSSProperties : undefined}
              >
                {/* Category Image */}
                <div className="relative h-32 w-full overflow-hidden">
                  <Image
                    src={category.image}
                    alt={`${category.name} category`}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                    sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
                    priority={false}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute bottom-0 left-0 right-0 p-3">
                    <h3 className="text-sm font-bold text-white group-hover:text-primary transition-colors">
                      {category.name}
                    </h3>
                  </div>
                </div>

                {/* Category Content */}
                <div className="p-3 flex-1 flex flex-col bg-card">
                  <p className="text-xs text-muted-foreground mb-2 flex-1 line-clamp-2">
                    {category.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">
                      {category.count} {category.count === 1 ? 'Post' : 'Posts'}
                    </span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary group-hover:translate-x-1 transition-transform"
                    >
                      <path d="M5 12h14"/>
                      <path d="m12 5 7 7-7 7"/>
                    </svg>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Infinite Scroll for More Posts */}
      {blogPosts.length > 11 && (
        <InfiniteScroll
          initialPosts={blogPosts.slice(11)}
        />
      )}
    </Container>
  )
}
